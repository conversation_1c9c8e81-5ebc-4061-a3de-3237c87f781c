<?php
/**
 * The template for displaying single product posts
 *
 * @package Light_Fixture_Child
 */

get_header();

// 开始WordPress循环
while (have_posts()) : the_post();

// 获取当前产品信息
$product_id = get_the_ID();

// 内存优化：获取产品图集图片 ID 列表
$memory_before = memory_get_usage(true);
$product_gallery_ids_string = get_post_meta($product_id, '_product_gallery_ids', true);
$gallery_ids = !empty($product_gallery_ids_string) ? explode(',', $product_gallery_ids_string) : array();
$product_gallery = array(); // 初始化为新的图集数据数组

// 限制图集数量以防止内存溢出
$max_gallery_images = 10; // 最多处理10张图片
$gallery_ids = array_slice($gallery_ids, 0, $max_gallery_images);

// 如果存在图集 ID，则遍历并获取图片 URL
if (!empty($gallery_ids)) {
    foreach ($gallery_ids as $index => $image_id) {
        // 内存检查：如果内存使用超过阈值，停止处理更多图片
        $current_memory = memory_get_usage(true);
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
        if ($current_memory > ($memory_limit * 0.8)) { // 使用80%内存时停止
            break;
        }

        $image_id = intval($image_id); // 确保 ID 是整数
        if ($image_id) {
            // 检查附件是否存在，避免无效查询
            if (!wp_attachment_is_image($image_id)) {
                continue;
            }

            // 获取全尺寸图片 URL
            $full_image_url = wp_get_attachment_image_url($image_id, 'full');
            // 获取缩略图 URL (使用 'thumbnail' 尺寸)
            $thumbnail_image_url = wp_get_attachment_image_url($image_id, 'thumbnail');

            // 确保获取到有效的 URL
            if ($full_image_url && $thumbnail_image_url) {
                $product_gallery[] = array(
                    'url' => $full_image_url,
                    'thumbnail' => $thumbnail_image_url
                );
            }
        }
    }
}



$product_sku = get_post_meta($product_id, '_product_sku', true);
$product_status = get_post_meta($product_id, '_product_status', true);
$product_specs = get_post_meta($product_id, '_product_specifications', true);
$product_features = get_post_meta($product_id, '_product_features', true);
$product_customization = get_post_meta($product_id, '_product_customization', true);
$product_fixed_desc = get_post_meta($product_id, '_product_fixed_desc', true);

// 获取产品分类
$product_categories = get_the_terms($product_id, 'product_category');
$category_name = $product_categories ? $product_categories[0]->name : '';

// 内存优化：获取智能相关产品推荐
$related_products = array();
try {
    // 检查内存使用情况
    $memory_usage = memory_get_usage(true);
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $available_memory = $memory_limit - $memory_usage;

    // 如果可用内存充足，使用智能推荐
    if ($available_memory > 67108864 && function_exists('light_fixture_get_smart_related_products')) { // 64MB
        $related_products = light_fixture_get_smart_related_products($product_id, 3);
    } else {
        // 使用简化的相关产品查询
        $related_products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => 3,
            'post__not_in' => array($product_id),
            'fields' => 'ids',
            'orderby' => 'rand',
            'no_found_rows' => true,
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false,
        ));

        // 将ID转换为post对象
        $related_post_objects = array();
        foreach ($related_products as $related_id) {
            $post_obj = get_post($related_id);
            if ($post_obj) {
                $related_post_objects[] = $post_obj;
            }
        }
        $related_products = $related_post_objects;
    }
} catch (Exception $e) {
    // 如果出现错误，使用空数组
    $related_products = array();
}
?>

<main id="main" class="site-main">
    <div class="container">
        <?php light_fixture_breadcrumbs(); ?>
        <!-- Product Detail -->
        <section class="product-detail">
            <div class="product-detail__gallery fade-in">
                <div class="product-detail__main-image">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('full', array(
                            'id' => 'mainProductImage',
                            'class' => 'product-main-image',
                            'alt' => get_the_title()
                        )); ?>
                    <?php else : ?>
                        <img id="mainProductImage" 
                             src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/placeholder.jpg" 
                             alt="<?php echo esc_attr(get_the_title()); ?>" 
                             class="product-main-image">
                    <?php endif; ?>
                </div>
                <?php if ($product_gallery) : ?>
                <div class="product-detail__thumbnails">
                    <?php foreach ($product_gallery as $index => $image) : ?>
                    <?php
                    // 优化缩略图URL
                    $optimized_thumbnail = $image['thumbnail'];
                    $optimized_full = $image['url'];
                    if (function_exists('light_fixture_get_optimized_image_url')) {
                        $optimized_thumbnail = light_fixture_get_optimized_image_url($image['thumbnail']);
                        $optimized_full = light_fixture_get_optimized_image_url($image['url']);
                    }
                    ?>
                    <div class="thumbnail-item <?php echo $index === 0 ? 'active' : ''; ?>" data-src="<?php echo esc_url($optimized_full); ?>">
                        <img src="<?php echo esc_url($optimized_thumbnail); ?>" alt="<?php echo esc_attr(get_the_title() . ' - View ' . ($index + 1)); ?>">
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="product-detail__info slide-up">
                <div class="product-detail__header">
                    <?php if ($category_name) : ?>
                    <span class="product-category"><?php echo esc_html($category_name); ?></span>
                    <?php endif; ?>
                    <h1 class="product-title"><?php the_title(); ?></h1>
                    <div class="product-meta">
                        <?php if ($product_sku) : ?>
                        <span class="product-sku">SKU: <?php echo esc_html($product_sku); ?></span>
                        <?php endif; ?>
                        <?php if ($product_status) : ?>
                        <span class="product-status"><?php echo esc_html($product_status); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($product_features) : ?>
                <div class="product-features">
                    <h3>产品特点</h3>
                    <ul>
                        <?php foreach ($product_features as $feature) : ?>
                        <li><?php echo esc_html($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if ($product_specs) : ?>
                <div class="product-specifications">
                    <h3>技术规格</h3>
                    <div class="specifications-table">
                        <?php foreach ($product_specs as $spec) : ?>
                        <div class="spec-row">
                            <div class="spec-label"><?php echo esc_html($spec['label']); ?></div>
                            <div class="spec-value"><?php echo esc_html($spec['value']); ?></div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($product_customization) : ?>
                <div class="product-customization">
                    <h3>Customization Options</h3>
                    <div class="customization-options">
                        <?php foreach ($product_customization as $option) : ?>
                        <div class="option-group">
                            <label><?php echo esc_html($option['label']); ?>:</label>
                            <div class="option-buttons">
                                <?php foreach ($option['values'] as $value) : ?>
                                <button class="option-button <?php echo $value['default'] ? 'active' : ''; ?>" 
                                        data-value="<?php echo esc_attr($value['value']); ?>" 
                                        aria-pressed="<?php echo $value['default'] ? 'true' : 'false'; ?>">
                                    <?php echo esc_html($value['label']); ?>
                                </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="product-actions">
                    <div class="inquiry-button-container">
                        <a href="<?php echo esc_url(home_url('/contact')); ?>" class="btn btn-primary inquiry-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                            Inquire / Customize
                        </a>
                    </div>

                    <!-- 产品页面社交媒体分享 -->
                    <section class="product-social-share" aria-labelledby="social-share-heading">
                        <h4 id="social-share-heading" class="social-share-title">Follow Us</h4>
                        <nav class="product-social-icons" role="navigation" aria-label="Social media links">
                            <?php
                            $social_links = get_social_media_links();
                            $platform_names = array(
                                'facebook' => 'Facebook',
                                'twitter' => 'Twitter',
                                'instagram' => 'Instagram',
                                'linkedin' => 'LinkedIn',
                                'youtube' => 'YouTube',
                                'pinterest' => 'Pinterest',
                                'tiktok' => 'TikTok',
                                'whatsapp' => 'WhatsApp'
                            );

                            if (!empty($social_links)) {
                                foreach ($social_links as $platform => $url) {
                                    $icon_svg = get_social_media_icon($platform);
                                    $platform_name = isset($platform_names[$platform]) ? $platform_names[$platform] : ucfirst($platform);

                                    if ($icon_svg) {
                                        echo '<a href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer nofollow" class="product-social-icon" aria-label="Follow ' . esc_attr(get_bloginfo('name')) . ' on ' . esc_attr($platform_name) . '" data-platform="' . esc_attr($platform) . '" title="' . esc_attr($platform_name) . '">';
                                        echo '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true" focusable="false">';
                                        echo $icon_svg;
                                        echo '</svg>';
                                        echo '<span class="social-tooltip">' . esc_html($platform_name) . '</span>';
                                        echo '<span class="sr-only">Follow ' . esc_html(get_bloginfo('name')) . ' on ' . esc_html($platform_name) . '</span>';
                                        echo '</a>';
                                    }
                                }
                            } else {
                                echo '<p class="no-social-links">No social media links available</p>';
                            }
                            ?>
                        </nav>
                    </section>
                </div>
            </div>
        </section>
        
        <?php if (!empty($product_fixed_desc)) : ?>
        <div class="product-fixed-description fade-in">
            <?php echo wp_kses_post($product_fixed_desc); ?>
        </div>
        <?php endif; ?>
        
        <div class="product-description">
            <?php the_content(); ?>
        </div>

        <!-- Related Products -->
        <?php if ($related_products) : ?>
        <section class="section">
            <header class="section-header text-center fade-in">
                <h2 class="section-title">相关产品</h2>
            </header>
            
            <div class="products-grid">
                <?php foreach ($related_products as $related) : ?>
                <div class="product-card slide-up">
                    <div class="product-card__image-container">
                        <?php if (has_post_thumbnail($related->ID)) : ?>
                        <?php
                        $thumbnail_url = get_the_post_thumbnail_url($related->ID, 'medium');
                        if (function_exists('light_fixture_get_optimized_image_url')) {
                            $thumbnail_url = light_fixture_get_optimized_image_url($thumbnail_url);
                        }
                        ?>
                        <img src="<?php echo esc_url($thumbnail_url); ?>"
                             alt="<?php echo esc_attr($related->post_title); ?>"
                             class="product-card__image">
                        <?php endif; ?>
                        <div class="product-card__overlay">
                            <a href="<?php echo get_permalink($related->ID); ?>" class="product-card__view-button">View Details</a>
                        </div>
                    </div>
                    <div class="product-card__content">
                        <h4 class="product-card__title"><?php echo esc_html($related->post_title); ?></h4>
                        <p class="product-card__description"><?php echo get_the_excerpt($related->ID); ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>
    </div>
</main>

<style>
/* 产品图集样式 - 移除高度限制，使用页面整体滚动 */

/* 产品固定描述样式 */
.product-fixed-description {
    margin-bottom: 2rem;
    line-height: 1.6;
}

.product-fixed-description p {
    margin-bottom: 1rem;
}

.product-fixed-description ul, 
.product-fixed-description ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.product-fixed-description img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
}

/* 产品页面社交媒体分享样式 */
.product-social-share {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 屏幕阅读器专用文本 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.social-share-title {
    font-family: "Playfair Display", "Georgia", serif;
    font-size: 1.125rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
}

.product-social-icons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    align-items: center;
}

.product-social-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 0; /* 方形设计，符合项目风格 */
    background-color: #ffffff;
    border: 2px solid #000000;
    color: #000000;
    text-decoration: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.product-social-icon:hover {
    background-color: #000000;
    border-color: #000000;
    color: #d6ad60; /* 金色图标 */
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.product-social-icon svg {
    transition: transform 0.3s ease;
}

.product-social-icon:hover svg {
    transform: scale(1.1);
}

/* 社交媒体工具提示 */
.social-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000000;
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
    z-index: 10;
}

.social-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #000000;
}

.product-social-icon:hover .social-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

.no-social-links {
    color: #666666;
    font-style: italic;
    font-size: 0.875rem;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 767px) {
    .product-social-icons {
        justify-content: center;
    }

    .product-social-icon {
        width: 44px;
        height: 44px;
    }

    .social-share-title {
        text-align: center;
        font-size: 1rem;
    }
}
</style>



<?php
endwhile; // 结束WordPress循环

// 添加社交媒体结构化数据
$social_links = get_social_media_links();
if (!empty($social_links)) {
    $social_urls = array_values($social_links);
    $organization_data = array(
        '@context' => 'https://schema.org',
        '@type' => 'Organization',
        'name' => get_bloginfo('name'),
        'url' => home_url(),
        'sameAs' => $social_urls
    );

    echo '<script type="application/ld+json">' . wp_json_encode($organization_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
}

get_footer();