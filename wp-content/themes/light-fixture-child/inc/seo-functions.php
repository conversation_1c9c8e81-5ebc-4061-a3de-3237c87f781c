<?php
/**
 * SEO优化功能
 * 
 * 为Light Fixture网站提供完整的SEO优化功能，包括：
 * - 产品页面SEO标题优化
 * - 产品结构化数据
 * - 智能图片alt标签生成
 * - 自动meta描述生成
 * - 面包屑结构化数据
 * 
 * @package Light_Fixture_Child
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 优化产品页面SEO标题
 */
function light_fixture_optimize_seo_title($title) {
    if (is_singular('product')) {
        global $post;
        $product_title = get_the_title();
        $category = get_the_terms($post->ID, 'product_category');
        $category_name = $category ? $category[0]->name : '';
        
        // 获取产品特点用于标题
        $features = get_post_meta($post->ID, '_product_features', true);
        $main_feature = $features && is_array($features) && !empty($features[0]) ? $features[0] : '';
        
        // SEO优化标题模板
        if ($category_name && $main_feature) {
            return sprintf('%s - %s %s | Light Fixture - Premium Designer Lighting', 
                          $product_title, 
                          $main_feature,
                          $category_name);
        } else {
            return sprintf('%s - %s | Light Fixture - Premium Designer Lighting', 
                          $product_title, 
                          $category_name);
        }
    }
    
    // 分类页面标题优化
    if (is_tax('product_category')) {
        $term = get_queried_object();
        $count = $term->count;
        return sprintf('%s - Premium %s Collection (%d+ Options) | Light Fixture', 
                      $term->name, 
                      $term->name, 
                      $count);
    }
    
    return $title;
}
add_filter('pre_get_document_title', 'light_fixture_optimize_seo_title');

/**
 * 自动生成SEO友好的图片Alt标签
 */
function light_fixture_auto_image_alt($attr, $attachment, $size) {
    // 如果已有alt标签，不覆盖
    if (!empty($attr['alt'])) {
        return $attr;
    }
    
    $post_id = get_the_ID();
    
    if (get_post_type($post_id) === 'product') {
        $product_title = get_the_title($post_id);
        $category = get_the_terms($post_id, 'product_category');
        $category_name = $category ? $category[0]->name : 'lighting';
        
        // 获取图片在产品图集中的位置
        $gallery_ids = get_post_meta($post_id, '_product_gallery_ids', true);
        $gallery_array = $gallery_ids ? explode(',', $gallery_ids) : array();
        $image_position = array_search($attachment->ID, $gallery_array);
        
        if ($image_position !== false) {
            $position_text = $image_position === 0 ? 'main view' : 'view ' . ($image_position + 1);
            $attr['alt'] = sprintf('%s - %s %s for modern interior design', 
                                 $product_title, 
                                 $category_name,
                                 $position_text);
        } else {
            $attr['alt'] = sprintf('%s - Premium %s for modern interior design', 
                                 $product_title, 
                                 $category_name);
        }
    }
    
    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'light_fixture_auto_image_alt', 10, 3);

/**
 * 为产品页面添加结构化数据
 */
function light_fixture_product_structured_data() {
    if (!is_singular('product')) {
        return;
    }
    
    global $post;
    $product_id = $post->ID;
    
    // 获取产品信息
    $product_title = get_the_title();
    $product_description = wp_strip_all_tags(get_the_content());
    $product_image = get_the_post_thumbnail_url($product_id, 'full');
    $category = get_the_terms($product_id, 'product_category');
    $sku = get_post_meta($product_id, '_product_sku', true);
    $features = get_post_meta($product_id, '_product_features', true);
    
    // 构建结构化数据
    $structured_data = array(
        '@context' => 'https://schema.org/',
        '@type' => 'Product',
        'name' => $product_title,
        'description' => $product_description,
        'image' => array($product_image),
        'brand' => array(
            '@type' => 'Brand',
            'name' => 'Light Fixture'
        ),
        'category' => $category ? $category[0]->name : 'Lighting'
    );
    
    // 添加SKU
    if ($sku) {
        $structured_data['sku'] = $sku;
    }
    
    // 添加产品特点
    if ($features && is_array($features)) {
        $structured_data['additionalProperty'] = array();
        foreach ($features as $feature) {
            if (!empty($feature)) {
                $structured_data['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'Feature',
                    'value' => $feature
                );
            }
        }
    }
    
    // 添加制造商信息
    $structured_data['manufacturer'] = array(
        '@type' => 'Organization',
        'name' => 'Light Fixture',
        'url' => home_url()
    );
    
    // 添加Offer信息（询价模式）
    $structured_data['offers'] = array(
        '@type' => 'Offer',
        'availability' => 'https://schema.org/InStock',
        'priceSpecification' => array(
            '@type' => 'PriceSpecification',
            'price' => 'Contact for pricing'
        ),
        'seller' => array(
            '@type' => 'Organization',
            'name' => 'Light Fixture'
        )
    );
    
    // 输出结构化数据
    echo '<script type="application/ld+json">';
    echo wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    echo '</script>' . "\n";
}
add_action('wp_head', 'light_fixture_product_structured_data');

/**
 * 自动生成产品页面Meta描述
 */
function light_fixture_auto_meta_description() {
    if (is_singular('product')) {
        global $post;
        
        // 获取产品信息
        $product_title = get_the_title();
        $category = get_the_terms($post->ID, 'product_category');
        $category_name = $category ? $category[0]->name : 'lighting';
        $features = get_post_meta($post->ID, '_product_features', true);
        $specs = get_post_meta($post->ID, '_product_specifications', true);
        
        // 构建描述
        $description = sprintf('Discover our %s - premium %s', $product_title, $category_name);
        
        if ($features && is_array($features) && count($features) > 0) {
            $valid_features = array_filter($features, function($feature) {
                return !empty($feature);
            });
            if (!empty($valid_features)) {
                $description .= ' featuring ' . implode(', ', array_slice($valid_features, 0, 2));
            }
        }
        
        if ($specs && is_array($specs) && isset($specs[0]['value']) && !empty($specs[0]['value'])) {
            $description .= '. ' . $specs[0]['value'];
        }
        
        $description .= '. Free shipping & expert installation. Shop premium designer lighting now!';
        
        // 确保描述长度在155字符以内
        if (strlen($description) > 155) {
            $description = substr($description, 0, 152) . '...';
        }
        
        echo '<meta name="description" content="' . esc_attr($description) . '">' . "\n";
    }
}
add_action('wp_head', 'light_fixture_auto_meta_description', 1);

/**
 * 为面包屑添加结构化数据
 */
function light_fixture_breadcrumb_structured_data() {
    if (is_front_page()) {
        return;
    }
    
    $breadcrumbs = array();
    $position = 1;
    
    // 首页
    $breadcrumbs[] = array(
        '@type' => 'ListItem',
        'position' => $position++,
        'name' => 'Home',
        'item' => home_url()
    );
    
    if (is_singular('product')) {
        // 产品归档页
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => 'All Products',
            'item' => get_post_type_archive_link('product')
        );
        
        // 产品分类
        $terms = get_the_terms(get_the_ID(), 'product_category');
        if ($terms && !is_wp_error($terms)) {
            $term = array_shift($terms);
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => $term->name,
                'item' => get_term_link($term)
            );
        }
        
        // 当前产品
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => get_the_title(),
            'item' => get_permalink()
        );
    }
    
    if (!empty($breadcrumbs)) {
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $breadcrumbs
        );
        
        echo '<script type="application/ld+json">';
        echo wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        echo '</script>' . "\n";
    }
}
add_action('wp_head', 'light_fixture_breadcrumb_structured_data');

/**
 * 智能相关产品推荐算法 - 内存优化版本
 */
function light_fixture_get_smart_related_products($product_id, $limit = 4) {
    // 内存检查
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_usage = memory_get_usage(true);
    $available_memory = $memory_limit - $memory_usage;

    // 如果可用内存少于32MB，使用简化版本
    if ($available_memory < 33554432) { // 32MB
        return light_fixture_get_simple_related_products($product_id, $limit);
    }

    // 检查缓存
    $cache_key = 'related_products_' . $product_id . '_' . $limit;
    $cached_result = wp_cache_get($cache_key, 'light_fixture_products');
    if ($cached_result !== false) {
        return $cached_result;
    }

    $current_categories = get_the_terms($product_id, 'product_category');

    if (!$current_categories || is_wp_error($current_categories)) {
        return array();
    }

    $category_ids = wp_list_pluck($current_categories, 'term_id');

    // 优化查询参数 - 只获取必要字段
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => $limit,
        'post__not_in' => array($product_id),
        'fields' => 'ids', // 只获取ID，减少内存使用
        'tax_query' => array(
            array(
                'taxonomy' => 'product_category',
                'field' => 'term_id',
                'terms' => $category_ids,
                'operator' => 'IN'
            )
        ),
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => '_product_status',
                'value' => 'In Stock',
                'compare' => '='
            ),
            array(
                'key' => '_product_featured',
                'value' => 'yes',
                'compare' => '='
            )
        ),
        'orderby' => 'menu_order',
        'order' => 'ASC',
        'no_found_rows' => true, // 不计算总行数，节省内存
        'update_post_meta_cache' => false, // 不预加载meta缓存
        'update_post_term_cache' => false, // 不预加载term缓存
    );

    $product_ids = get_posts($args);

    // 如果同分类产品不够，从其他分类补充
    if (count($product_ids) < $limit) {
        $additional_args = array(
            'post_type' => 'product',
            'posts_per_page' => $limit - count($product_ids),
            'post__not_in' => array_merge(array($product_id), $product_ids),
            'fields' => 'ids',
            'meta_query' => array(
                array(
                    'key' => '_product_featured',
                    'value' => 'yes',
                    'compare' => '='
                )
            ),
            'orderby' => 'rand',
            'no_found_rows' => true,
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false,
        );

        $additional_ids = get_posts($additional_args);
        $product_ids = array_merge($product_ids, $additional_ids);
    }

    // 将ID转换为post对象（只在需要时）
    $related_products = array();
    foreach (array_slice($product_ids, 0, $limit) as $id) {
        $post = get_post($id);
        if ($post) {
            $related_products[] = $post;
        }
    }

    // 缓存结果（缓存1小时）
    wp_cache_set($cache_key, $related_products, 'light_fixture_products', 3600);

    return $related_products;
}

/**
 * 简化版相关产品推荐 - 低内存版本
 */
function light_fixture_get_simple_related_products($product_id, $limit = 4) {
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => $limit,
        'post__not_in' => array($product_id),
        'fields' => 'ids',
        'orderby' => 'rand',
        'no_found_rows' => true,
        'update_post_meta_cache' => false,
        'update_post_term_cache' => false,
    );

    $product_ids = get_posts($args);
    $related_products = array();

    foreach ($product_ids as $id) {
        $post = get_post($id);
        if ($post) {
            $related_products[] = $post;
        }
    }

    return $related_products;
}

/**
 * 在产品描述中自动添加内部链接
 */
function light_fixture_auto_internal_links($content) {
    if (!is_singular('product')) {
        return $content;
    }

    // 定义关键词和对应的链接
    $internal_links = array(
        'pendant lights' => get_term_link('pendant-lights', 'product_category'),
        'table lamps' => get_term_link('table-lamps', 'product_category'),
        'floor lamps' => get_term_link('floor-lamps', 'product_category'),
        'wall sconces' => get_term_link('wall-sconces', 'product_category'),
        'modern lighting' => get_post_type_archive_link('product'),
        'designer lighting' => get_post_type_archive_link('product'),
        'contemporary lighting' => get_post_type_archive_link('product'),
        'luxury lighting' => get_post_type_archive_link('product')
    );

    foreach ($internal_links as $keyword => $url) {
        if ($url && !is_wp_error($url)) {
            // 只在第一次出现时添加链接，避免过度优化
            $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i';
            $replacement = '<a href="' . esc_url($url) . '" title="' . esc_attr($keyword) . '">' . $keyword . '</a>';
            $content = preg_replace($pattern, $replacement, $content, 1);
        }
    }

    return $content;
}
add_filter('the_content', 'light_fixture_auto_internal_links');

/**
 * SEO监控仪表板
 */
function light_fixture_seo_dashboard_widget() {
    wp_add_dashboard_widget(
        'light_fixture_seo_stats',
        'SEO优化状态',
        'light_fixture_seo_stats_display'
    );
}
add_action('wp_dashboard_setup', 'light_fixture_seo_dashboard_widget');



function light_fixture_seo_stats_display() {
    // 获取产品总数
    $total_products = wp_count_posts('product')->publish;

    // 获取有特色图片的产品数
    $products_with_images = get_posts(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        ),
        'fields' => 'ids'
    ));

    // 获取有产品特点的产品数
    $products_with_features = get_posts(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_product_features',
                'compare' => 'EXISTS'
            )
        ),
        'fields' => 'ids'
    ));

    // 获取有分类的产品数
    $products_with_categories = get_posts(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'product_category',
                'operator' => 'EXISTS'
            )
        ),
        'fields' => 'ids'
    ));

    echo '<div class="seo-stats" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">';
    echo '<div>';
    echo '<h4>📊 产品SEO状态</h4>';
    echo '<p><strong>总产品数:</strong> ' . $total_products . '</p>';
    echo '<p><strong>有特色图片:</strong> ' . count($products_with_images) . ' (' . round((count($products_with_images) / $total_products) * 100, 1) . '%)</p>';
    echo '<p><strong>有产品特点:</strong> ' . count($products_with_features) . ' (' . round((count($products_with_features) / $total_products) * 100, 1) . '%)</p>';
    echo '<p><strong>有分类标签:</strong> ' . count($products_with_categories) . ' (' . round((count($products_with_categories) / $total_products) * 100, 1) . '%)</p>';
    echo '</div>';

    echo '<div>';
    echo '<h4>🚀 SEO功能状态</h4>';
    echo '<p><strong>动态标题:</strong> <span style="color: green;">✅ 已启用</span></p>';
    echo '<p><strong>结构化数据:</strong> <span style="color: green;">✅ 已启用</span></p>';
    echo '<p><strong>智能Alt标签:</strong> <span style="color: green;">✅ 已启用</span></p>';
    echo '<p><strong>自动Meta描述:</strong> <span style="color: green;">✅ 已启用</span></p>';
    echo '</div>';
    echo '</div>';

    echo '<div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-left: 4px solid #0073aa;">';
    echo '<p><strong>💡 优化建议:</strong></p>';
    if (count($products_with_images) < $total_products) {
        echo '<p>• 为 ' . ($total_products - count($products_with_images)) . ' 个产品添加特色图片</p>';
    }
    if (count($products_with_features) < $total_products) {
        echo '<p>• 为 ' . ($total_products - count($products_with_features)) . ' 个产品添加产品特点</p>';
    }
    if (count($products_with_categories) < $total_products) {
        echo '<p>• 为 ' . ($total_products - count($products_with_categories)) . ' 个产品设置分类</p>';
    }
    echo '</div>';
}


