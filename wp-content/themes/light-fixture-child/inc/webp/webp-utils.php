<?php
/**
 * WebP Utility Functions
 * WebP工具函数库
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 检查文件是否为支持的图片格式
 * 
 * @param string $file_path 文件路径
 * @return bool
 */
function light_fixture_is_supported_image($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $image_info = wp_getimagesize($file_path);
    if (!$image_info) {
        return false;
    }
    
    $supported_mimes = array(
        'image/jpeg',
        'image/png', 
        'image/gif'
    );
    
    return in_array($image_info['mime'], $supported_mimes);
}

/**
 * 获取图片的WebP版本路径
 * 
 * @param string $original_path 原始图片路径
 * @return string WebP文件路径
 */
function light_fixture_get_webp_path($original_path) {
    $path_info = pathinfo($original_path);
    return $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';
}

/**
 * 检查WebP文件是否存在
 * 
 * @param string $original_path 原始图片路径
 * @return bool
 */
function light_fixture_webp_exists($original_path) {
    $webp_path = light_fixture_get_webp_path($original_path);
    return file_exists($webp_path);
}

/**
 * 获取图片文件大小比较信息
 * 
 * @param string $original_path 原始图片路径
 * @return array|false 包含大小比较信息的数组，失败返回false
 */
function light_fixture_get_size_comparison($original_path) {
    if (!file_exists($original_path)) {
        return false;
    }
    
    $webp_path = light_fixture_get_webp_path($original_path);
    if (!file_exists($webp_path)) {
        return false;
    }
    
    $original_size = filesize($original_path);
    $webp_size = filesize($webp_path);
    
    $savings = $original_size - $webp_size;
    $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 2) : 0;
    
    return array(
        'original_size' => $original_size,
        'webp_size' => $webp_size,
        'savings_bytes' => $savings,
        'savings_percent' => $savings_percent,
        'original_size_formatted' => size_format($original_size),
        'webp_size_formatted' => size_format($webp_size),
        'savings_formatted' => size_format($savings)
    );
}

/**
 * 获取附件的所有WebP文件路径
 * 
 * @param int $attachment_id 附件ID
 * @return array WebP文件路径数组
 */
function light_fixture_get_attachment_webp_paths($attachment_id) {
    $webp_paths = array();
    
    // 获取原始文件路径
    $original_file = get_attached_file($attachment_id);
    if ($original_file) {
        $webp_paths['full'] = light_fixture_get_webp_path($original_file);
    }
    
    // 获取所有尺寸的WebP路径
    $metadata = wp_get_attachment_metadata($attachment_id);
    if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
        $upload_dir = wp_upload_dir();
        $base_dir = dirname($original_file);
        
        foreach ($metadata['sizes'] as $size_name => $size_data) {
            $size_file_path = $base_dir . '/' . $size_data['file'];
            $webp_paths[$size_name] = light_fixture_get_webp_path($size_file_path);
        }
    }
    
    return $webp_paths;
}

/**
 * 检查附件是否已有WebP版本
 * 
 * @param int $attachment_id 附件ID
 * @return array 包含各尺寸WebP存在状态的数组
 */
function light_fixture_check_attachment_webp_status($attachment_id) {
    $webp_paths = light_fixture_get_attachment_webp_paths($attachment_id);
    $status = array();
    
    foreach ($webp_paths as $size => $path) {
        $status[$size] = file_exists($path);
    }
    
    return $status;
}

/**
 * 获取WebP转换统计信息
 * 
 * @return array 统计信息数组
 */
function light_fixture_get_webp_statistics() {
    global $wpdb;
    
    // 获取所有图片附件
    $attachments = $wpdb->get_results(
        "SELECT ID FROM {$wpdb->posts} 
         WHERE post_type = 'attachment' 
         AND post_mime_type LIKE 'image/%'
         AND post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')"
    );
    
    $stats = array(
        'total_images' => count($attachments),
        'webp_converted' => 0,
        'total_original_size' => 0,
        'total_webp_size' => 0,
        'total_savings' => 0,
        'conversion_rate' => 0
    );
    
    foreach ($attachments as $attachment) {
        $original_file = get_attached_file($attachment->ID);
        if (!$original_file || !file_exists($original_file)) {
            continue;
        }
        
        $stats['total_original_size'] += filesize($original_file);
        
        if (light_fixture_webp_exists($original_file)) {
            $stats['webp_converted']++;
            $webp_path = light_fixture_get_webp_path($original_file);
            $stats['total_webp_size'] += filesize($webp_path);
        }
    }
    
    $stats['total_savings'] = $stats['total_original_size'] - $stats['total_webp_size'];
    $stats['conversion_rate'] = $stats['total_images'] > 0 ? 
        round(($stats['webp_converted'] / $stats['total_images']) * 100, 2) : 0;
    
    // 格式化大小
    $stats['total_original_size_formatted'] = size_format($stats['total_original_size']);
    $stats['total_webp_size_formatted'] = size_format($stats['total_webp_size']);
    $stats['total_savings_formatted'] = size_format($stats['total_savings']);
    
    return $stats;
}

/**
 * 清理孤立的WebP文件
 * 
 * @return int 清理的文件数量
 */
function light_fixture_cleanup_orphaned_webp() {
    $upload_dir = wp_upload_dir();
    $base_dir = $upload_dir['basedir'];
    
    $cleaned_count = 0;
    
    // 递归查找所有WebP文件
    $webp_files = light_fixture_find_webp_files($base_dir);
    
    foreach ($webp_files as $webp_file) {
        // 检查对应的原始文件是否存在
        $original_file = light_fixture_get_original_from_webp($webp_file);
        
        if (!file_exists($original_file)) {
            // 原始文件不存在，删除WebP文件
            if (wp_delete_file($webp_file)) {
                $cleaned_count++;
            }
        }
    }
    
    return $cleaned_count;
}

/**
 * 递归查找目录中的所有WebP文件
 * 
 * @param string $dir 目录路径
 * @return array WebP文件路径数组
 */
function light_fixture_find_webp_files($dir) {
    $webp_files = array();
    
    if (!is_dir($dir)) {
        return $webp_files;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'webp') {
            $webp_files[] = $file->getPathname();
        }
    }
    
    return $webp_files;
}

/**
 * 从WebP文件路径获取对应的原始文件路径
 * 
 * @param string $webp_path WebP文件路径
 * @return string 原始文件路径
 */
function light_fixture_get_original_from_webp($webp_path) {
    $path_info = pathinfo($webp_path);
    $base_path = $path_info['dirname'] . '/' . $path_info['filename'];
    
    // 尝试常见的图片扩展名
    $extensions = array('jpg', 'jpeg', 'png', 'gif');
    
    foreach ($extensions as $ext) {
        $original_path = $base_path . '.' . $ext;
        if (file_exists($original_path)) {
            return $original_path;
        }
    }
    
    // 如果没找到，返回最可能的jpg路径
    return $base_path . '.jpg';
}

/**
 * 验证WebP文件的完整性
 * 
 * @param string $webp_path WebP文件路径
 * @return bool
 */
function light_fixture_validate_webp_file($webp_path) {
    if (!file_exists($webp_path)) {
        return false;
    }
    
    // 检查文件大小
    if (filesize($webp_path) === 0) {
        return false;
    }
    
    // 检查文件头
    $handle = fopen($webp_path, 'rb');
    if (!$handle) {
        return false;
    }
    
    $header = fread($handle, 12);
    fclose($handle);
    
    // WebP文件应该以RIFF开头，并在第8-11字节包含WEBP
    if (substr($header, 0, 4) !== 'RIFF' || substr($header, 8, 4) !== 'WEBP') {
        return false;
    }
    
    return true;
}

/**
 * 获取WebP转换队列中的图片数量
 * 
 * @return int 待转换的图片数量
 */
function light_fixture_get_conversion_queue_count() {
    global $wpdb;
    
    $attachments = $wpdb->get_results(
        "SELECT ID FROM {$wpdb->posts} 
         WHERE post_type = 'attachment' 
         AND post_mime_type LIKE 'image/%'
         AND post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')"
    );
    
    $queue_count = 0;
    
    foreach ($attachments as $attachment) {
        $original_file = get_attached_file($attachment->ID);
        if ($original_file && file_exists($original_file) && !light_fixture_webp_exists($original_file)) {
            $queue_count++;
        }
    }
    
    return $queue_count;
}

/**
 * 格式化文件大小节省百分比
 * 
 * @param float $percent 百分比
 * @return string 格式化的百分比字符串
 */
function light_fixture_format_savings_percent($percent) {
    if ($percent > 0) {
        return '<span style="color: green;">-' . $percent . '%</span>';
    } elseif ($percent < 0) {
        return '<span style="color: red;">+' . abs($percent) . '%</span>';
    } else {
        return '<span style="color: gray;">0%</span>';
    }
}



/**
 * 获取优化的图片URL (WebP版本) - 高性能版本
 *
 * @param string $original_url 原始图片URL
 * @return string 优化后的图片URL
 */
function light_fixture_get_optimized_image_url($original_url) {
    // 检查浏览器是否支持WebP
    if (!light_fixture_is_webp_browser_supported()) {
        return $original_url;
    }

    // 检查是否已经是WebP格式
    if (preg_match('/\.webp(\?.*)?$/i', $original_url)) {
        return $original_url; // 已经是WebP格式，直接返回
    }

    // 检查是否为可转换的图片格式
    if (!preg_match('/\.(jpe?g|png|gif)(\?.*)?$/i', $original_url)) {
        return $original_url; // 不是支持的图片格式
    }

    // 使用缓存来避免重复的文件系统检查
    static $webp_cache = array();
    $cache_key = md5($original_url);

    if (isset($webp_cache[$cache_key])) {
        return $webp_cache[$cache_key];
    }

    // 生成WebP URL
    $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $original_url);

    // 检查WebP文件是否存在
    $upload_dir = wp_upload_dir();
    $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

    // 处理主题文件路径
    if (strpos($webp_url, get_stylesheet_directory_uri()) !== false) {
        $webp_path = str_replace(get_stylesheet_directory_uri(), get_stylesheet_directory(), $webp_url);
    }

    // 清理路径中的双斜杠
    $webp_path = str_replace('//', '/', $webp_path);
    $webp_path = str_replace('\\\\', '\\', $webp_path);

    $result_url = $original_url;
    if (file_exists($webp_path)) {
        $result_url = $webp_url;
    }

    // 缓存结果
    $webp_cache[$cache_key] = $result_url;

    return $result_url;
}

/**
 * 获取WebP URL（快速版本，带对象缓存）
 *
 * @param string $original_url 原始图片URL
 * @return string 优化后的图片URL
 */
function light_fixture_get_webp_url_fast($original_url) {
    // 检查浏览器是否支持WebP
    if (!light_fixture_is_webp_browser_supported()) {
        return $original_url;
    }

    // 检查是否已经是WebP格式
    if (preg_match('/\.webp(\?.*)?$/i', $original_url)) {
        return $original_url; // 已经是WebP格式，直接返回
    }

    // 检查是否为可转换的图片格式
    if (!preg_match('/\.(jpe?g|png|gif)(\?.*)?$/i', $original_url)) {
        return $original_url; // 不是支持的图片格式
    }

    // 使用对象缓存来避免重复的文件系统检查
    $cache_key = 'webp_url_' . md5($original_url);
    $cached_result = wp_cache_get($cache_key, 'webp_urls');

    if ($cached_result !== false) {
        return $cached_result;
    }

    // 生成WebP URL
    $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $original_url);

    // 检查WebP文件是否存在
    $upload_dir = wp_upload_dir();
    $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

    $result_url = $original_url;
    if (file_exists($webp_path)) {
        $result_url = $webp_url;
    }

    // 缓存结果（1小时）
    wp_cache_set($cache_key, $result_url, 'webp_urls', 3600);

    return $result_url;
}

/**
 * 预加载关键WebP图片
 */
function light_fixture_preload_critical_webp_images() {
    if (!light_fixture_is_webp_browser_supported()) {
        return;
    }

    $critical_images = light_fixture_get_critical_images();

    if (empty($critical_images)) {
        return;
    }

    echo "<!-- WebP Critical Images Preload -->\n";

    foreach ($critical_images as $image_data) {
        $webp_url = light_fixture_get_webp_url_fast($image_data['url']);
        if ($webp_url !== $image_data['url']) {
            echo '<link rel="preload" as="image" href="' . esc_url($webp_url) . '" type="image/webp">' . "\n";
        }
    }
}

/**
 * 获取关键图片列表
 */
function light_fixture_get_critical_images() {
    // 缓存关键图片列表
    $cache_key = 'webp_critical_images';
    $cached_images = wp_cache_get($cache_key, 'webp');

    if ($cached_images !== false) {
        return $cached_images;
    }

    $critical_images = array();

    // 获取首页特色图片
    if (is_front_page()) {
        $hero_image_id = get_theme_mod('hero_image');
        if ($hero_image_id) {
            $hero_url = wp_get_attachment_image_src($hero_image_id, 'hero-background');
            if ($hero_url) {
                $critical_images[] = array(
                    'url' => $hero_url[0],
                    'priority' => 'high',
                    'type' => 'hero'
                );
            }
        }
    }

    // 获取产品页面的主图片
    if (is_singular('product')) {
        $product_image_id = get_post_thumbnail_id();
        if ($product_image_id) {
            $product_url = wp_get_attachment_image_src($product_image_id, 'product-gallery');
            if ($product_url) {
                $critical_images[] = array(
                    'url' => $product_url[0],
                    'priority' => 'high',
                    'type' => 'product'
                );
            }
        }
    }

    // 获取文章特色图片
    if (is_singular('post')) {
        $post_image_id = get_post_thumbnail_id();
        if ($post_image_id) {
            $post_url = wp_get_attachment_image_src($post_image_id, 'large');
            if ($post_url) {
                $critical_images[] = array(
                    'url' => $post_url[0],
                    'priority' => 'medium',
                    'type' => 'post'
                );
            }
        }
    }

    // 缓存结果（30分钟）
    wp_cache_set($cache_key, $critical_images, 'webp', 1800);

    return $critical_images;
}

/**
 * 批量预热WebP缓存
 */
function light_fixture_warm_up_webp_cache() {
    global $wpdb;

    // 获取最近的图片附件
    $recent_attachments = $wpdb->get_results(
        "SELECT ID FROM {$wpdb->posts}
         WHERE post_type = 'attachment'
         AND post_mime_type LIKE 'image/%'
         ORDER BY post_date DESC
         LIMIT 100"
    );

    foreach ($recent_attachments as $attachment) {
        $sizes = array('thumbnail', 'medium', 'large');

        foreach ($sizes as $size) {
            $image_src = wp_get_attachment_image_src($attachment->ID, $size);
            if ($image_src) {
                // 预热缓存
                light_fixture_get_webp_url_fast($image_src[0]);
            }
        }
    }
}

/**
 * 清理WebP缓存
 */
function light_fixture_cleanup_webp_cache() {
    // 清理对象缓存
    wp_cache_flush_group('webp_urls');
    wp_cache_flush_group('webp');
    wp_cache_flush_group('webp_detection');
}


