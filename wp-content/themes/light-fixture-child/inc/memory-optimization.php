<?php
/**
 * 内存优化辅助函数
 *
 * @package Light_Fixture_Child
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 检查可用内存是否足够执行操作
 *
 * @param int $required_bytes 需要的字节数
 * @return bool 是否有足够内存
 */
function light_fixture_check_memory_available($required_bytes = 33554432) { // 默认32MB
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_usage = memory_get_usage(true);
    $available_memory = $memory_limit - $memory_usage;
    
    return $available_memory >= $required_bytes;
}

/**
 * 安全地获取产品元数据，带内存检查
 *
 * @param int $product_id 产品ID
 * @param string $meta_key 元数据键
 * @param mixed $default 默认值
 * @return mixed 元数据值或默认值
 */
function light_fixture_safe_get_product_meta($product_id, $meta_key, $default = '') {
    if (!light_fixture_check_memory_available(1048576)) { // 1MB
        return $default;
    }
    
    try {
        return get_post_meta($product_id, $meta_key, true);
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * 内存友好的图片URL获取
 *
 * @param int $attachment_id 附件ID
 * @param string $size 图片尺寸
 * @return string|false 图片URL或false
 */
function light_fixture_safe_get_image_url($attachment_id, $size = 'thumbnail') {
    if (!light_fixture_check_memory_available(2097152)) { // 2MB
        return false;
    }
    
    // 检查附件是否存在
    if (!wp_attachment_is_image($attachment_id)) {
        return false;
    }
    
    try {
        return wp_get_attachment_image_url($attachment_id, $size);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 内存优化的产品查询
 *
 * @param array $args 查询参数
 * @return array 产品数组
 */
function light_fixture_memory_optimized_product_query($args = array()) {
    $default_args = array(
        'post_type' => 'product',
        'posts_per_page' => 5,
        'fields' => 'ids',
        'no_found_rows' => true,
        'update_post_meta_cache' => false,
        'update_post_term_cache' => false,
    );
    
    $args = wp_parse_args($args, $default_args);
    
    // 如果内存不足，进一步限制查询
    if (!light_fixture_check_memory_available(16777216)) { // 16MB
        $args['posts_per_page'] = min($args['posts_per_page'], 3);
    }
    
    try {
        return get_posts($args);
    } catch (Exception $e) {
        return array();
    }
}

/**
 * 清理未使用的缓存和临时数据
 */
function light_fixture_cleanup_memory() {
    // 清理WordPress对象缓存
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('light_fixture_products');
    }
    
    // 强制垃圾回收
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
}

/**
 * 产品页面内存优化钩子
 */
function light_fixture_product_page_memory_optimization() {
    if (is_singular('product')) {
        // 在产品页面开始时清理内存
        light_fixture_cleanup_memory();
        
        // 在页面结束时清理内存
        add_action('wp_footer', function() {
            light_fixture_cleanup_memory();
        }, 999);
    }
}
add_action('template_redirect', 'light_fixture_product_page_memory_optimization');

/**
 * 紧急内存清理 - 当内存使用过高时调用
 */
function light_fixture_emergency_memory_cleanup() {
    // 清理所有缓存
    wp_cache_flush();
    
    // 清理临时数据
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('light_fixture_products');
        wp_cache_flush_group('posts');
        wp_cache_flush_group('post_meta');
    }
    
    // 强制垃圾回收
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
    

}

/**
 * 内存使用监控和自动清理
 */
function light_fixture_auto_memory_monitor() {
    $memory_usage = memory_get_usage(true);
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $usage_percent = ($memory_usage / $memory_limit) * 100;
    
    // 如果内存使用超过85%，执行紧急清理
    if ($usage_percent > 85) {
        light_fixture_emergency_memory_cleanup();
    }
}

// 在每个页面请求结束时检查内存使用
add_action('shutdown', 'light_fixture_auto_memory_monitor');
